/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define CPUCLK_FREQ                                                     32000000




/* Defines for COMP_0 */
#define COMP_0_INST                                                        COMP2
#define COMP_0_INST_INT_IRQN                                      COMP2_INT_IRQn


/* GPIO configuration for COMP_0 */
#define GPIO_COMP_0_IN0P_PORT                                            (GPIOB)
#define GPIO_COMP_0_IN0P_PIN                                    (DL_GPIO_PIN_21)
#define GPIO_COMP_0_IOMUX_IN0P                                   (IOMUX_PINCM49)
#define GPIO_COMP_0_IOMUX_IN0P_FUNC               (IOMUX_PINCM49_PF_UNCONNECTED)

#define GPIO_COMP_0_IN0N_PORT                                            (GPIOB)
#define GPIO_COMP_0_IN0N_PIN                                    (DL_GPIO_PIN_22)
#define GPIO_COMP_0_IOMUX_IN0N                                   (IOMUX_PINCM50)
#define GPIO_COMP_0_IOMUX_IN0N_FUNC               (IOMUX_PINCM50_PF_UNCONNECTED)

#define GPIO_COMP_0_OUT_PORT                                               GPIOA
#define GPIO_COMP_0_OUT_PIN                                       DL_GPIO_PIN_16
#define GPIO_COMP_0_IOMUX_OUT                                    (IOMUX_PINCM38)
#define GPIO_COMP_0_IOMUX_OUT_FUNC                    IOMUX_PINCM38_PF_COMP2_OUT



/* Port definition for Pin Group GPIO_GRP_B */
#define GPIO_GRP_B_PORT                                                  (GPIOB)

/* Defines for PIN_CS: GPIOB.4 with pinCMx 17 on package pin 52 */
#define GPIO_GRP_B_PIN_CS_PIN                                    (DL_GPIO_PIN_4)
#define GPIO_GRP_B_PIN_CS_IOMUX                                  (IOMUX_PINCM17)
/* Defines for PIN_UPDATE: GPIOB.5 with pinCMx 18 on package pin 53 */
#define GPIO_GRP_B_PIN_UPDATE_PIN                                (DL_GPIO_PIN_5)
#define GPIO_GRP_B_PIN_UPDATE_IOMUX                              (IOMUX_PINCM18)
/* Defines for PIN_PS0: GPIOB.2 with pinCMx 15 on package pin 50 */
#define GPIO_GRP_B_PIN_PS0_PIN                                   (DL_GPIO_PIN_2)
#define GPIO_GRP_B_PIN_PS0_IOMUX                                 (IOMUX_PINCM15)
/* Defines for PIN_PS1: GPIOB.16 with pinCMx 33 on package pin 4 */
#define GPIO_GRP_B_PIN_PS1_PIN                                  (DL_GPIO_PIN_16)
#define GPIO_GRP_B_PIN_PS1_IOMUX                                 (IOMUX_PINCM33)
/* Defines for PIN_PS2: GPIOB.15 with pinCMx 32 on package pin 3 */
#define GPIO_GRP_B_PIN_PS2_PIN                                  (DL_GPIO_PIN_15)
#define GPIO_GRP_B_PIN_PS2_IOMUX                                 (IOMUX_PINCM32)
/* Port definition for Pin Group GPIO_GRP_A */
#define GPIO_GRP_A_PORT                                                  (GPIOA)

/* Defines for PIN_SCLK: GPIOA.31 with pinCMx 6 on package pin 39 */
#define GPIO_GRP_A_PIN_SCLK_PIN                                 (DL_GPIO_PIN_31)
#define GPIO_GRP_A_PIN_SCLK_IOMUX                                 (IOMUX_PINCM6)
/* Defines for PIN_PS3: GPIOA.9 with pinCMx 20 on package pin 55 */
#define GPIO_GRP_A_PIN_PS3_PIN                                   (DL_GPIO_PIN_9)
#define GPIO_GRP_A_PIN_PS3_IOMUX                                 (IOMUX_PINCM20)
/* Defines for PIN_SDIO0: GPIOA.8 with pinCMx 19 on package pin 54 */
#define GPIO_GRP_A_PIN_SDIO0_PIN                                 (DL_GPIO_PIN_8)
#define GPIO_GRP_A_PIN_SDIO0_IOMUX                               (IOMUX_PINCM19)
/* Defines for PIN_SDIO1: GPIOA.1 with pinCMx 2 on package pin 34 */
#define GPIO_GRP_A_PIN_SDIO1_PIN                                 (DL_GPIO_PIN_1)
#define GPIO_GRP_A_PIN_SDIO1_IOMUX                                (IOMUX_PINCM2)
/* Defines for PIN_SDIO2: GPIOA.0 with pinCMx 1 on package pin 33 */
#define GPIO_GRP_A_PIN_SDIO2_PIN                                 (DL_GPIO_PIN_0)
#define GPIO_GRP_A_PIN_SDIO2_IOMUX                                (IOMUX_PINCM1)
/* Defines for PIN_SDIO3: GPIOA.28 with pinCMx 3 on package pin 35 */
#define GPIO_GRP_A_PIN_SDIO3_PIN                                (DL_GPIO_PIN_28)
#define GPIO_GRP_A_PIN_SDIO3_IOMUX                                (IOMUX_PINCM3)
/* Defines for PIN_AD9959_PWR: GPIOA.11 with pinCMx 22 on package pin 57 */
#define GPIO_GRP_A_PIN_AD9959_PWR_PIN                           (DL_GPIO_PIN_11)
#define GPIO_GRP_A_PIN_AD9959_PWR_IOMUX                          (IOMUX_PINCM22)
/* Defines for PIN_Reset: GPIOA.10 with pinCMx 21 on package pin 56 */
#define GPIO_GRP_A_PIN_Reset_PIN                                (DL_GPIO_PIN_10)
#define GPIO_GRP_A_PIN_Reset_IOMUX                               (IOMUX_PINCM21)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_COMP_0_init(void);



#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
