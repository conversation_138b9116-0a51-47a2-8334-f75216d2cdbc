<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v3.2.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <link_time>0x687baeb3</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\AD9959_ctrl\Debug\AD9959_ctrl.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x6e5</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\AD9959_ctrl\Debug\.\</path>
         <kind>object</kind>
         <file>AD9959.o</file>
         <name>AD9959.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\AD9959_ctrl\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\AD9959_ctrl\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\AD9959_ctrl\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\Users\<USER>\workspace_ccstheia\AD9959_ctrl\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-12">
         <path>D:\TISDK\mspm0_sdk_2_00_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-21">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-22">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-23">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-24">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-25">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-26">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-27">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-28">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-29">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-2a">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-2b">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-91">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-92">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-93">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixunsdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-94">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsidf.S.obj</name>
      </input_file>
      <input_file id="fl-95">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-96">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-97">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-98">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.text.AD9959_WriteData</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text.__muldf3</name>
         <load_address>0x1ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ec</run_address>
         <size>0xe4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-91"/>
      </object_component>
      <object_component id="oc-76">
         <name>.text:memcpy</name>
         <load_address>0x2d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2d0</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-97"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x36a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x36a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x36c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x36c</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text.AD9959_Init</name>
         <load_address>0x3e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3e4</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text.main</name>
         <load_address>0x450</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x450</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x4ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4ac</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.text.SYSCFG_DL_COMP_0_init</name>
         <load_address>0x4fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4fc</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.text.__fixunsdfsi</name>
         <load_address>0x544</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x544</run_address>
         <size>0x42</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-93"/>
      </object_component>
      <object_component id="oc-85">
         <name>.text.delay1</name>
         <load_address>0x586</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x586</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-86">
         <name>.text.AD9959_Set_Fre</name>
         <load_address>0x588</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x588</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-af">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x5c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5c8</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-94">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x608</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x608</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.text.__muldsi3</name>
         <load_address>0x644</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x644</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-92"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x680</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x680</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-88">
         <name>.text.AD9959_Set_Amp</name>
         <load_address>0x6b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b4</run_address>
         <size>0x2e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x6e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6e4</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-89">
         <name>.text.AD9959_Set_Phase</name>
         <load_address>0x70c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x70c</run_address>
         <size>0x24</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text.__floatunsidf</name>
         <load_address>0x730</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x730</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.text.IO_Update</name>
         <load_address>0x754</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x754</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x768</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x768</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x77c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x77c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x78e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x78e</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x798</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x798</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x7a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7a0</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.text._system_pre_init</name>
         <load_address>0x7a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7a4</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.text:abort</name>
         <load_address>0x7a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7a8</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.cinit..data.load</name>
         <load_address>0x7b0</load_address>
         <readonly>true</readonly>
         <run_address>0x7b0</run_address>
         <size>0x11</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-12a">
         <name>__TI_handler_table</name>
         <load_address>0x7c4</load_address>
         <readonly>true</readonly>
         <run_address>0x7c4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-12b">
         <name>__TI_cinit_table</name>
         <load_address>0x7cc</load_address>
         <readonly>true</readonly>
         <run_address>0x7cc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f4">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-b5">
         <name>.data.FR1_DATA</name>
         <load_address>0x20200008</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200008</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.data.FR2_DATA</name>
         <load_address>0x2020000b</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020000b</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.data.ACC_FRE_FACTOR</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x8</size>
         <alignment>0x8</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x190e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_loc</name>
         <load_address>0x190e</load_address>
         <run_address>0x190e</run_address>
         <size>0xc3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_loc</name>
         <load_address>0x19d1</load_address>
         <run_address>0x19d1</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_loc</name>
         <load_address>0x19e4</load_address>
         <run_address>0x19e4</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_loc</name>
         <load_address>0x1abc</load_address>
         <run_address>0x1abc</run_address>
         <size>0x480</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_loc</name>
         <load_address>0x1f3c</load_address>
         <run_address>0x1f3c</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_loc</name>
         <load_address>0x1fab</load_address>
         <run_address>0x1fab</run_address>
         <size>0x166</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_loc</name>
         <load_address>0x2111</load_address>
         <run_address>0x2111</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x24a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_abbrev</name>
         <load_address>0x24a</load_address>
         <run_address>0x24a</run_address>
         <size>0x47</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_abbrev</name>
         <load_address>0x291</load_address>
         <run_address>0x291</run_address>
         <size>0x1ec</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_abbrev</name>
         <load_address>0x47d</load_address>
         <run_address>0x47d</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_abbrev</name>
         <load_address>0x4ea</load_address>
         <run_address>0x4ea</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_abbrev</name>
         <load_address>0x54c</load_address>
         <run_address>0x54c</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_abbrev</name>
         <load_address>0x5fb</load_address>
         <run_address>0x5fb</run_address>
         <size>0x186</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_abbrev</name>
         <load_address>0x781</load_address>
         <run_address>0x781</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_abbrev</name>
         <load_address>0x7ba</load_address>
         <run_address>0x7ba</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0x82a</load_address>
         <run_address>0x82a</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_abbrev</name>
         <load_address>0x8b7</load_address>
         <run_address>0x8b7</run_address>
         <size>0xb3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_abbrev</name>
         <load_address>0x96a</load_address>
         <run_address>0x96a</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-91"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_abbrev</name>
         <load_address>0x991</load_address>
         <run_address>0x991</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-92"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_abbrev</name>
         <load_address>0x9b8</load_address>
         <run_address>0x9b8</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-93"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_abbrev</name>
         <load_address>0x9df</load_address>
         <run_address>0x9df</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_abbrev</name>
         <load_address>0xa06</load_address>
         <run_address>0xa06</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_abbrev</name>
         <load_address>0xa2d</load_address>
         <run_address>0xa2d</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-97"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_abbrev</name>
         <load_address>0xa52</load_address>
         <run_address>0xa52</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1dce</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_info</name>
         <load_address>0x1dce</load_address>
         <run_address>0x1dce</run_address>
         <size>0xab</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_info</name>
         <load_address>0x1e79</load_address>
         <run_address>0x1e79</run_address>
         <size>0x2990</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x4809</load_address>
         <run_address>0x4809</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_info</name>
         <load_address>0x4889</load_address>
         <run_address>0x4889</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x48fe</load_address>
         <run_address>0x48fe</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_info</name>
         <load_address>0x4d21</load_address>
         <run_address>0x4d21</run_address>
         <size>0x74a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_info</name>
         <load_address>0x546b</load_address>
         <run_address>0x546b</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0x54b1</load_address>
         <run_address>0x54b1</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0x5577</load_address>
         <run_address>0x5577</run_address>
         <size>0x180</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_info</name>
         <load_address>0x56f7</load_address>
         <run_address>0x56f7</run_address>
         <size>0xed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_info</name>
         <load_address>0x57e4</load_address>
         <run_address>0x57e4</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-91"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_info</name>
         <load_address>0x5976</load_address>
         <run_address>0x5976</run_address>
         <size>0x194</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-92"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_info</name>
         <load_address>0x5b0a</load_address>
         <run_address>0x5b0a</run_address>
         <size>0x19a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-93"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_info</name>
         <load_address>0x5ca4</load_address>
         <run_address>0x5ca4</run_address>
         <size>0x19c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_info</name>
         <load_address>0x5e40</load_address>
         <run_address>0x5e40</run_address>
         <size>0x19e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_info</name>
         <load_address>0x5fde</load_address>
         <run_address>0x5fde</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-97"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_info</name>
         <load_address>0x62d8</load_address>
         <run_address>0x62d8</run_address>
         <size>0xa7</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x440</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_ranges</name>
         <load_address>0x440</load_address>
         <run_address>0x440</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x470</load_address>
         <run_address>0x470</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_ranges</name>
         <load_address>0x488</load_address>
         <run_address>0x488</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_ranges</name>
         <load_address>0x4d0</load_address>
         <run_address>0x4d0</run_address>
         <size>0xa8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_ranges</name>
         <load_address>0x578</load_address>
         <run_address>0x578</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_ranges</name>
         <load_address>0x5a8</load_address>
         <run_address>0x5a8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_ranges</name>
         <load_address>0x5c0</load_address>
         <run_address>0x5c0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-97"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_str</name>
         <load_address>0x7a6</load_address>
         <run_address>0x7a6</run_address>
         <size>0x11b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_str</name>
         <load_address>0x8c1</load_address>
         <run_address>0x8c1</run_address>
         <size>0x1bb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_str</name>
         <load_address>0x247a</load_address>
         <run_address>0x247a</run_address>
         <size>0x161</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_str</name>
         <load_address>0x25db</load_address>
         <run_address>0x25db</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_str</name>
         <load_address>0x2752</load_address>
         <run_address>0x2752</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_str</name>
         <load_address>0x2977</load_address>
         <run_address>0x2977</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_str</name>
         <load_address>0x2ca6</load_address>
         <run_address>0x2ca6</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_str</name>
         <load_address>0x2d9b</load_address>
         <run_address>0x2d9b</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x2f03</load_address>
         <run_address>0x2f03</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_str</name>
         <load_address>0x30d8</load_address>
         <run_address>0x30d8</run_address>
         <size>0x13f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2c4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_frame</name>
         <load_address>0x2c4</load_address>
         <run_address>0x2c4</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_frame</name>
         <load_address>0x2e4</load_address>
         <run_address>0x2e4</run_address>
         <size>0x70</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x354</load_address>
         <run_address>0x354</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_frame</name>
         <load_address>0x384</load_address>
         <run_address>0x384</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_frame</name>
         <load_address>0x3a4</load_address>
         <run_address>0x3a4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_frame</name>
         <load_address>0x434</load_address>
         <run_address>0x434</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_frame</name>
         <load_address>0x534</load_address>
         <run_address>0x534</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0x554</load_address>
         <run_address>0x554</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0x57c</load_address>
         <run_address>0x57c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_frame</name>
         <load_address>0x5ac</load_address>
         <run_address>0x5ac</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1040</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_line</name>
         <load_address>0x1040</load_address>
         <run_address>0x1040</run_address>
         <size>0x77</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_line</name>
         <load_address>0x10b7</load_address>
         <run_address>0x10b7</run_address>
         <size>0x44b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x1502</load_address>
         <run_address>0x1502</run_address>
         <size>0xbd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_line</name>
         <load_address>0x15bf</load_address>
         <run_address>0x15bf</run_address>
         <size>0xe4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x16a3</load_address>
         <run_address>0x16a3</run_address>
         <size>0x1fe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_line</name>
         <load_address>0x18a1</load_address>
         <run_address>0x18a1</run_address>
         <size>0x4fb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_line</name>
         <load_address>0x1d9c</load_address>
         <run_address>0x1d9c</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x1dda</load_address>
         <run_address>0x1dda</run_address>
         <size>0xbf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x1e99</load_address>
         <run_address>0x1e99</run_address>
         <size>0x1c7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_line</name>
         <load_address>0x2060</load_address>
         <run_address>0x2060</run_address>
         <size>0x6b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_line</name>
         <load_address>0x20cb</load_address>
         <run_address>0x20cb</run_address>
         <size>0x10c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-91"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_line</name>
         <load_address>0x21d7</load_address>
         <run_address>0x21d7</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-92"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_line</name>
         <load_address>0x2290</load_address>
         <run_address>0x2290</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-93"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_line</name>
         <load_address>0x2350</load_address>
         <run_address>0x2350</run_address>
         <size>0xb2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_line</name>
         <load_address>0x2402</load_address>
         <run_address>0x2402</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_line</name>
         <load_address>0x24a6</load_address>
         <run_address>0x24a6</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-97"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-91"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-92"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-93"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-97"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x6f0</size>
         <contents>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-8d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x7b0</load_address>
         <run_address>0x7b0</run_address>
         <size>0x28</size>
         <contents>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-12b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-f4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0xd</size>
         <contents>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-c5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-12e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-eb" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-ec" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-ed" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-ee" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-ef" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f0" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f2" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10e" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2137</size>
         <contents>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-93"/>
         </contents>
      </logical_group>
      <logical_group id="lg-110" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa61</size>
         <contents>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-130"/>
         </contents>
      </logical_group>
      <logical_group id="lg-112" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x637f</size>
         <contents>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-12f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-114" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5e8</size>
         <contents>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-75"/>
         </contents>
      </logical_group>
      <logical_group id="lg-116" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3217</size>
         <contents>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-c7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-118" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5dc</size>
         <contents>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-91"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11a" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2546</size>
         <contents>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-73"/>
         </contents>
      </logical_group>
      <logical_group id="lg-124" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc8</size>
         <contents>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-72"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12d" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-136" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7d8</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-137" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0xd</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-138" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x7d8</used_space>
         <unused_space>0x1f828</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x6f0</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x7b0</start_address>
               <size>0x28</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x7d8</start_address>
               <size>0x1f828</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x20d</used_space>
         <unused_space>0x7df3</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-f0"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-f2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0xd</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x2020000d</start_address>
               <size>0x7df3</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x7b0</load_address>
            <load_size>0x11</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0xd</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x7cc</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x7d4</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x7d4</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x7c4</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x7cc</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-4c">
         <name>AD9959_Init</name>
         <value>0x3e5</value>
         <object_component_ref idref="oc-80"/>
      </symbol>
      <symbol id="sm-4d">
         <name>AD9959_WriteData</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-b4"/>
      </symbol>
      <symbol id="sm-4e">
         <name>FR1_DATA</name>
         <value>0x20200008</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-4f">
         <name>FR2_DATA</name>
         <value>0x2020000b</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-50">
         <name>delay1</name>
         <value>0x587</value>
         <object_component_ref idref="oc-85"/>
      </symbol>
      <symbol id="sm-51">
         <name>IO_Update</name>
         <value>0x755</value>
         <object_component_ref idref="oc-8a"/>
      </symbol>
      <symbol id="sm-52">
         <name>ACC_FRE_FACTOR</name>
         <value>0x20200000</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-53">
         <name>AD9959_Set_Fre</name>
         <value>0x589</value>
         <object_component_ref idref="oc-86"/>
      </symbol>
      <symbol id="sm-54">
         <name>AD9959_Set_Amp</name>
         <value>0x6b5</value>
         <object_component_ref idref="oc-88"/>
      </symbol>
      <symbol id="sm-55">
         <name>AD9959_Set_Phase</name>
         <value>0x70d</value>
         <object_component_ref idref="oc-89"/>
      </symbol>
      <symbol id="sm-5e">
         <name>main</name>
         <value>0x451</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-74">
         <name>SYSCFG_DL_init</name>
         <value>0x769</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-75">
         <name>SYSCFG_DL_initPower</name>
         <value>0x681</value>
         <object_component_ref idref="oc-ad"/>
      </symbol>
      <symbol id="sm-76">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x4ad</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-77">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x5c9</value>
         <object_component_ref idref="oc-af"/>
      </symbol>
      <symbol id="sm-78">
         <name>SYSCFG_DL_COMP_0_init</name>
         <value>0x4fd</value>
         <object_component_ref idref="oc-b1"/>
      </symbol>
      <symbol id="sm-83">
         <name>Default_Handler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>Reset_Handler</name>
         <value>0x7a1</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-85">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-86">
         <name>NMI_Handler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>HardFault_Handler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>SVC_Handler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>PendSV_Handler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>SysTick_Handler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>GROUP0_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8c">
         <name>GROUP1_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>TIMG8_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>UART3_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8f">
         <name>ADC0_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-90">
         <name>ADC1_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-91">
         <name>CANFD0_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-92">
         <name>DAC0_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-93">
         <name>SPI0_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-94">
         <name>SPI1_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-95">
         <name>UART1_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-96">
         <name>UART2_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-97">
         <name>UART0_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-98">
         <name>TIMG0_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-99">
         <name>TIMG6_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9a">
         <name>TIMA0_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9b">
         <name>TIMA1_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9c">
         <name>TIMG7_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9d">
         <name>TIMG12_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9e">
         <name>I2C0_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9f">
         <name>I2C1_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a0">
         <name>AES_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a1">
         <name>RTC_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a2">
         <name>DMA_IRQHandler</name>
         <value>0x36b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a3">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a4">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a5">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a6">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a7">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a8">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a9">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-aa">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ab">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b4">
         <name>DL_Common_delayCycles</name>
         <value>0x78f</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-bf">
         <name>_c_int00_noargs</name>
         <value>0x6e5</value>
         <object_component_ref idref="oc-4f"/>
      </symbol>
      <symbol id="sm-c0">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-cc">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x609</value>
         <object_component_ref idref="oc-94"/>
      </symbol>
      <symbol id="sm-d4">
         <name>_system_pre_init</name>
         <value>0x7a5</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-df">
         <name>__TI_decompress_none</name>
         <value>0x77d</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-ea">
         <name>__TI_decompress_lzss</name>
         <value>0x36d</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-f4">
         <name>abort</name>
         <value>0x7a9</value>
         <object_component_ref idref="oc-8d"/>
      </symbol>
      <symbol id="sm-f5">
         <name>C$$EXIT</name>
         <value>0x7a8</value>
         <object_component_ref idref="oc-8d"/>
      </symbol>
      <symbol id="sm-fd">
         <name>__aeabi_dmul</name>
         <value>0x1ed</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-fe">
         <name>__muldf3</name>
         <value>0x1ed</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-104">
         <name>__muldsi3</name>
         <value>0x645</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-10a">
         <name>__aeabi_d2uiz</name>
         <value>0x545</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-10b">
         <name>__fixunsdfsi</name>
         <value>0x545</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-111">
         <name>__aeabi_ui2d</name>
         <value>0x731</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-112">
         <name>__floatunsidf</name>
         <value>0x731</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-118">
         <name>__aeabi_memcpy</name>
         <value>0x799</value>
         <object_component_ref idref="oc-3f"/>
      </symbol>
      <symbol id="sm-119">
         <name>__aeabi_memcpy4</name>
         <value>0x799</value>
         <object_component_ref idref="oc-3f"/>
      </symbol>
      <symbol id="sm-11a">
         <name>__aeabi_memcpy8</name>
         <value>0x799</value>
         <object_component_ref idref="oc-3f"/>
      </symbol>
      <symbol id="sm-133">
         <name>memcpy</name>
         <value>0x2d1</value>
         <object_component_ref idref="oc-76"/>
      </symbol>
      <symbol id="sm-134">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-137">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-138">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
