################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
%.o: ../%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/ticccs/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O2 -I"C:/Users/<USER>/workspace_ccstheia/AD9959_ctrl" -I"C:/Users/<USER>/workspace_ccstheia/AD9959_ctrl/Debug" -I"D:/TISDK/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/Core/Include" -I"D:/TISDK/mspm0_sdk_2_00_01_00/source" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

build-437183231: ../empty.syscfg
	@echo 'Building file: "$<"'
	@echo 'Invoking: SysConfig'
	"D:/ticccs/ccs/utils/sysconfig_1.20.0/sysconfig_cli.bat" --script "C:/Users/<USER>/workspace_ccstheia/AD9959_ctrl/empty.syscfg" -o "." -s "D:/TISDK/mspm0_sdk_2_00_01_00/.metadata/product.json" -s "D:/TISDK/mspm0_sdk_2_00_01_00/.metadata/product.json" --compiler ticlang
	@echo 'Finished building: "$<"'
	@echo ' '

device_linker.cmd: build-437183231 ../empty.syscfg
device.opt: build-437183231
device.cmd.genlibs: build-437183231
ti_msp_dl_config.c: build-437183231
ti_msp_dl_config.h: build-437183231
Event.dot: build-437183231

%.o: ./%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/ticccs/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O2 -I"C:/Users/<USER>/workspace_ccstheia/AD9959_ctrl" -I"C:/Users/<USER>/workspace_ccstheia/AD9959_ctrl/Debug" -I"D:/TISDK/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/Core/Include" -I"D:/TISDK/mspm0_sdk_2_00_01_00/source" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

startup_mspm0g350x_ticlang.o: D:/TISDK/mspm0_sdk_2_00_01_00/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/ticccs/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O2 -I"C:/Users/<USER>/workspace_ccstheia/AD9959_ctrl" -I"C:/Users/<USER>/workspace_ccstheia/AD9959_ctrl/Debug" -I"D:/TISDK/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/Core/Include" -I"D:/TISDK/mspm0_sdk_2_00_01_00/source" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


