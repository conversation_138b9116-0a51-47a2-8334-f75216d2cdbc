/*
 * Copyright (c) 2023, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.c =============
 *  Configured MSPM0 DriverLib module definitions
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */

#include "ti_msp_dl_config.h"

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform any initialization needed before using any board APIs
 */
SYSCONFIG_WEAK void SYSCFG_DL_init(void)
{
    SYSCFG_DL_initPower();
    SYSCFG_DL_GPIO_init();
    /* Module-Specific Initializations*/
    SYSCFG_DL_SYSCTL_init();
    SYSCFG_DL_COMP_0_init();
}

SYSCONFIG_WEAK void SYSCFG_DL_initPower(void)
{
    DL_GPIO_reset(GPIOA);
    DL_GPIO_reset(GPIOB);
    DL_COMP_reset(COMP_0_INST);

    DL_GPIO_enablePower(GPIOA);
    DL_GPIO_enablePower(GPIOB);
    DL_COMP_enablePower(COMP_0_INST);
    delay_cycles(POWER_STARTUP_DELAY);
}

SYSCONFIG_WEAK void SYSCFG_DL_GPIO_init(void)
{

    DL_GPIO_initPeripheralOutputFunction(
        GPIO_COMP_0_IOMUX_OUT, GPIO_COMP_0_IOMUX_OUT_FUNC);

    DL_GPIO_initDigitalOutput(GPIO_GRP_B_PIN_CS_IOMUX);

    DL_GPIO_initDigitalOutput(GPIO_GRP_B_PIN_UPDATE_IOMUX);

    DL_GPIO_initDigitalOutput(GPIO_GRP_B_PIN_PS0_IOMUX);

    DL_GPIO_initDigitalOutput(GPIO_GRP_B_PIN_PS1_IOMUX);

    DL_GPIO_initDigitalOutput(GPIO_GRP_B_PIN_PS2_IOMUX);

    DL_GPIO_initDigitalOutput(GPIO_GRP_A_PIN_SCLK_IOMUX);

    DL_GPIO_initDigitalOutput(GPIO_GRP_A_PIN_PS3_IOMUX);

    DL_GPIO_initDigitalOutput(GPIO_GRP_A_PIN_SDIO0_IOMUX);

    DL_GPIO_initDigitalOutput(GPIO_GRP_A_PIN_SDIO1_IOMUX);

    DL_GPIO_initDigitalOutput(GPIO_GRP_A_PIN_SDIO2_IOMUX);

    DL_GPIO_initDigitalOutput(GPIO_GRP_A_PIN_SDIO3_IOMUX);

    DL_GPIO_initDigitalOutput(GPIO_GRP_A_PIN_AD9959_PWR_IOMUX);

    DL_GPIO_initDigitalOutput(GPIO_GRP_A_PIN_Reset_IOMUX);

    DL_GPIO_clearPins(GPIO_GRP_A_PORT, GPIO_GRP_A_PIN_SCLK_PIN |
		GPIO_GRP_A_PIN_PS3_PIN |
		GPIO_GRP_A_PIN_SDIO0_PIN |
		GPIO_GRP_A_PIN_SDIO1_PIN |
		GPIO_GRP_A_PIN_SDIO2_PIN |
		GPIO_GRP_A_PIN_SDIO3_PIN |
		GPIO_GRP_A_PIN_AD9959_PWR_PIN |
		GPIO_GRP_A_PIN_Reset_PIN);
    DL_GPIO_enableOutput(GPIO_GRP_A_PORT, GPIO_GRP_A_PIN_SCLK_PIN |
		GPIO_GRP_A_PIN_PS3_PIN |
		GPIO_GRP_A_PIN_SDIO0_PIN |
		GPIO_GRP_A_PIN_SDIO1_PIN |
		GPIO_GRP_A_PIN_SDIO2_PIN |
		GPIO_GRP_A_PIN_SDIO3_PIN |
		GPIO_GRP_A_PIN_AD9959_PWR_PIN |
		GPIO_GRP_A_PIN_Reset_PIN);
    DL_GPIO_clearPins(GPIO_GRP_B_PORT, GPIO_GRP_B_PIN_CS_PIN |
		GPIO_GRP_B_PIN_UPDATE_PIN |
		GPIO_GRP_B_PIN_PS0_PIN |
		GPIO_GRP_B_PIN_PS1_PIN |
		GPIO_GRP_B_PIN_PS2_PIN);
    DL_GPIO_enableOutput(GPIO_GRP_B_PORT, GPIO_GRP_B_PIN_CS_PIN |
		GPIO_GRP_B_PIN_UPDATE_PIN |
		GPIO_GRP_B_PIN_PS0_PIN |
		GPIO_GRP_B_PIN_PS1_PIN |
		GPIO_GRP_B_PIN_PS2_PIN);

}


SYSCONFIG_WEAK void SYSCFG_DL_SYSCTL_init(void)
{

	//Low Power Mode is configured to be SLEEP0
    DL_SYSCTL_setBORThreshold(DL_SYSCTL_BOR_THRESHOLD_LEVEL_0);

    DL_SYSCTL_setSYSOSCFreq(DL_SYSCTL_SYSOSC_FREQ_BASE);
    /* Set default configuration */
    DL_SYSCTL_disableHFXT();
    DL_SYSCTL_disableSYSPLL();
    DL_SYSCTL_setULPCLKDivider(DL_SYSCTL_ULPCLK_DIV_1);
    DL_SYSCTL_setMCLKDivider(DL_SYSCTL_MCLK_DIVIDER_DISABLE);

}


/* COMP_0 Initialization */
static const DL_COMP_Config gCOMP_0Config = {
    .channelEnable = DL_COMP_ENABLE_CHANNEL_POS_NEG,
    .mode          = DL_COMP_MODE_FAST,
    .negChannel    = DL_COMP_IMSEL_CHANNEL_0,
    .posChannel    = DL_COMP_IPSEL_CHANNEL_0,
    .hysteresis    = DL_COMP_HYSTERESIS_30,
    .polarity      = DL_COMP_POLARITY_NON_INV
};
static const DL_COMP_RefVoltageConfig gCOMP_0VRefConfig = {
    .mode           = DL_COMP_REF_MODE_STATIC,
    .source         = DL_COMP_REF_SOURCE_NONE,
    .terminalSelect = DL_COMP_REF_TERMINAL_SELECT_POS,
    .controlSelect  = DL_COMP_DAC_CONTROL_COMP_OUT,
    .inputSelect    = DL_COMP_DAC_INPUT_DACCODE0
};

SYSCONFIG_WEAK void SYSCFG_DL_COMP_0_init(void)
{
    DL_COMP_init(COMP_0_INST, (DL_COMP_Config *) &gCOMP_0Config);
    DL_COMP_enableOutputFilter(COMP_0_INST,DL_COMP_FILTER_DELAY_70);
    DL_COMP_refVoltageInit(COMP_0_INST, (DL_COMP_RefVoltageConfig *) &gCOMP_0VRefConfig);

    DL_COMP_enable(COMP_0_INST);

}


