/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.20.0+3587"}
 */

/**
 * Import the modules used in this configuration.
 */
const COMP   = scripting.addModule("/ti/driverlib/COMP", {}, false);
const COMP1  = COMP.addInstance();
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");

/**
 * Write custom configuration values to the imported modules.
 */
COMP1.$name                   = "COMP_0";
COMP1.outputEnable            = true;
COMP1.channelEnable           = ["NEG","POS"];
COMP1.hysteresis              = "DL_COMP_HYSTERESIS_30";
COMP1.enableOutputFilter      = true;
COMP1.peripheral.$assign      = "COMP2";
COMP1.compPinOutConfig.$name  = "ti_driverlib_gpio_GPIOPinGeneric0";
COMP1.compPinPos0Config.$name = "ti_driverlib_gpio_GPIOPinGeneric1";
COMP1.compPinNeg0Config.$name = "ti_driverlib_gpio_GPIOPinGeneric2";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.$name                          = "GPIO_GRP_B";
GPIO1.port                           = "PORTB";
GPIO1.associatedPins.create(5);
GPIO1.associatedPins[0].$name        = "PIN_CS";
GPIO1.associatedPins[0].assignedPort = "PORTB";
GPIO1.associatedPins[0].assignedPin  = "4";
GPIO1.associatedPins[1].$name        = "PIN_UPDATE";
GPIO1.associatedPins[1].assignedPort = "PORTB";
GPIO1.associatedPins[1].assignedPin  = "5";
GPIO1.associatedPins[2].$name        = "PIN_PS0";
GPIO1.associatedPins[2].assignedPort = "PORTB";
GPIO1.associatedPins[2].assignedPin  = "2";
GPIO1.associatedPins[3].$name        = "PIN_PS1";
GPIO1.associatedPins[3].assignedPort = "PORTB";
GPIO1.associatedPins[3].assignedPin  = "16";
GPIO1.associatedPins[4].$name        = "PIN_PS2";
GPIO1.associatedPins[4].assignedPort = "PORTB";
GPIO1.associatedPins[4].assignedPin  = "15";

GPIO2.$name                          = "GPIO_GRP_A";
GPIO2.port                           = "PORTA";
GPIO2.associatedPins.create(8);
GPIO2.associatedPins[0].$name        = "PIN_SCLK";
GPIO2.associatedPins[0].assignedPort = "PORTA";
GPIO2.associatedPins[0].assignedPin  = "31";
GPIO2.associatedPins[1].$name        = "PIN_PS3";
GPIO2.associatedPins[1].assignedPin  = "9";
GPIO2.associatedPins[2].$name        = "PIN_SDIO0";
GPIO2.associatedPins[2].assignedPin  = "8";
GPIO2.associatedPins[3].$name        = "PIN_SDIO1";
GPIO2.associatedPins[3].assignedPin  = "1";
GPIO2.associatedPins[4].$name        = "PIN_SDIO2";
GPIO2.associatedPins[4].assignedPin  = "0";
GPIO2.associatedPins[5].$name        = "PIN_SDIO3";
GPIO2.associatedPins[5].assignedPin  = "28";
GPIO2.associatedPins[6].$name        = "PIN_AD9959_PWR";
GPIO2.associatedPins[6].assignedPin  = "11";
GPIO2.associatedPins[7].$name        = "PIN_Reset";
GPIO2.associatedPins[7].assignedPin  = "10";

SYSCTL.forceDefaultClkConfig = true;

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
COMP1.peripheral.compPinOut.$suggestSolution  = "PA16";
COMP1.peripheral.compPinPos0.$suggestSolution = "PB21";
COMP1.peripheral.compPinNeg0.$suggestSolution = "PB22";
Board.peripheral.$suggestSolution             = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution    = "PA20";
Board.peripheral.swdioPin.$suggestSolution    = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution  = "PB4";
GPIO1.associatedPins[1].pin.$suggestSolution  = "PB5";
GPIO1.associatedPins[2].pin.$suggestSolution  = "PB2";
GPIO1.associatedPins[3].pin.$suggestSolution  = "PB16";
GPIO1.associatedPins[4].pin.$suggestSolution  = "PB15";
GPIO2.associatedPins[0].pin.$suggestSolution  = "PA31";
GPIO2.associatedPins[1].pin.$suggestSolution  = "PA9";
GPIO2.associatedPins[2].pin.$suggestSolution  = "PA8";
GPIO2.associatedPins[3].pin.$suggestSolution  = "PA1";
GPIO2.associatedPins[4].pin.$suggestSolution  = "PA0";
GPIO2.associatedPins[5].pin.$suggestSolution  = "PA28";
GPIO2.associatedPins[6].pin.$suggestSolution  = "PA11";
GPIO2.associatedPins[7].pin.$suggestSolution  = "PA10";
SYSCTL.peripheral.$suggestSolution            = "SYSCTL";
