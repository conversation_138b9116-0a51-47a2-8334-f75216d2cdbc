<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v3.2.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <link_time>0x6821dfc6</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noinit_noargs</name>
      <address>0x17d</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-20">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-21">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-22">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-23">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-24">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-25">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-26">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-27">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-28">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-29">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-2a">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-90">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-91">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-92">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-93">
         <path>D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.text.SYSCFG_DL_COMP_0_init</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x108</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x108</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x148</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x148</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.text:_c_int00_noinit_noargs</name>
         <load_address>0x17c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x19c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19c</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text.main</name>
         <load_address>0x1b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b0</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x1c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c4</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x1d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d0</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x1da</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1da</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.text._system_pre_init</name>
         <load_address>0x1de</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1de</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-82">
         <name>.text:abort</name>
         <load_address>0x1e2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x1e6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e6</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-106">
         <name>__TI_cinit_table</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-cf">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-108">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xee</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_abbrev</name>
         <load_address>0xee</load_address>
         <run_address>0xee</run_address>
         <size>0x1ec</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_abbrev</name>
         <load_address>0x2da</load_address>
         <run_address>0x2da</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_abbrev</name>
         <load_address>0x347</load_address>
         <run_address>0x347</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_abbrev</name>
         <load_address>0x3a9</load_address>
         <run_address>0x3a9</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_abbrev</name>
         <load_address>0x458</load_address>
         <run_address>0x458</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_abbrev</name>
         <load_address>0x491</load_address>
         <run_address>0x491</run_address>
         <size>0xb3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_abbrev</name>
         <load_address>0x544</load_address>
         <run_address>0x544</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3b4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_info</name>
         <load_address>0x3b4</load_address>
         <run_address>0x3b4</run_address>
         <size>0x2796</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x2b4a</load_address>
         <run_address>0x2b4a</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_info</name>
         <load_address>0x2bca</load_address>
         <run_address>0x2bca</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x2c3f</load_address>
         <run_address>0x2c3f</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_info</name>
         <load_address>0x3062</load_address>
         <run_address>0x3062</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_info</name>
         <load_address>0x30a8</load_address>
         <run_address>0x30a8</run_address>
         <size>0xed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_info</name>
         <load_address>0x3195</load_address>
         <run_address>0x3195</run_address>
         <size>0xbe</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x24a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_str</name>
         <load_address>0x24a</load_address>
         <run_address>0x24a</run_address>
         <size>0x1b8a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_str</name>
         <load_address>0x1dd4</load_address>
         <run_address>0x1dd4</run_address>
         <size>0x175</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_str</name>
         <load_address>0x1f49</load_address>
         <run_address>0x1f49</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_str</name>
         <load_address>0x20c0</load_address>
         <run_address>0x20c0</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_str</name>
         <load_address>0x22e5</load_address>
         <run_address>0x22e5</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_str</name>
         <load_address>0x23da</load_address>
         <run_address>0x23da</run_address>
         <size>0x13f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_frame</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x70</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x90</load_address>
         <run_address>0x90</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_frame</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_frame</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_frame</name>
         <load_address>0x170</load_address>
         <run_address>0x170</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_frame</name>
         <load_address>0x190</load_address>
         <run_address>0x190</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1c9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_line</name>
         <load_address>0x1c9</load_address>
         <run_address>0x1c9</run_address>
         <size>0x413</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0x5dc</load_address>
         <run_address>0x5dc</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_line</name>
         <load_address>0x696</load_address>
         <run_address>0x696</run_address>
         <size>0xe4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x77a</load_address>
         <run_address>0x77a</run_address>
         <size>0x1fe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_line</name>
         <load_address>0x978</load_address>
         <run_address>0x978</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0x9b6</load_address>
         <run_address>0x9b6</run_address>
         <size>0x6b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_loc</name>
         <load_address>0xc3</load_address>
         <run_address>0xc3</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_loc</name>
         <load_address>0xd6</load_address>
         <run_address>0xd6</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_loc</name>
         <load_address>0x1ae</load_address>
         <run_address>0x1ae</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_ranges</name>
         <load_address>0x30</load_address>
         <run_address>0x30</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_ranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_ranges</name>
         <load_address>0x90</load_address>
         <run_address>0x90</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x128</size>
         <contents>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-33"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-106"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-cf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-108"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c6" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c7" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c8" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c9" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-ca" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-cb" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-cd" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e9" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x553</size>
         <contents>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-10a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-eb" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3253</size>
         <contents>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-109"/>
         </contents>
      </logical_group>
      <logical_group id="lg-ed" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2519</size>
         <contents>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-aa"/>
         </contents>
      </logical_group>
      <logical_group id="lg-ef" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1c0</size>
         <contents>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-85"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f1" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa21</size>
         <contents>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-83"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f3" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1d4</size>
         <contents>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-88"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f5" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa8</size>
         <contents>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-84"/>
         </contents>
      </logical_group>
      <logical_group id="lg-107" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-10b" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1e8</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
         </contents>
      </load_segment>
      <load_segment id="lg-10c" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x1e8</used_space>
         <unused_space>0x1fe18</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x128</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <available_space>
               <start_address>0x1e8</start_address>
               <size>0x1fe18</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x200</used_space>
         <unused_space>0x7e00</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-cb"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-cd"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200000</start_address>
               <size>0x7e00</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-8">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-9">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-a">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-b">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-c">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-d">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-39">
         <name>main</name>
         <value>0x1b1</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-4f">
         <name>SYSCFG_DL_init</name>
         <value>0x19d</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-50">
         <name>SYSCFG_DL_initPower</name>
         <value>0x149</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-51">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x1c5</value>
         <object_component_ref idref="oc-a3"/>
      </symbol>
      <symbol id="sm-52">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x109</value>
         <object_component_ref idref="oc-a4"/>
      </symbol>
      <symbol id="sm-53">
         <name>SYSCFG_DL_COMP_0_init</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-5e">
         <name>Default_Handler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-5f">
         <name>Reset_Handler</name>
         <value>0x1db</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-60">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-61">
         <name>NMI_Handler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-62">
         <name>HardFault_Handler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-63">
         <name>SVC_Handler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-64">
         <name>PendSV_Handler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-65">
         <name>SysTick_Handler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-66">
         <name>GROUP0_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-67">
         <name>GROUP1_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-68">
         <name>TIMG8_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-69">
         <name>UART3_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6a">
         <name>ADC0_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6b">
         <name>ADC1_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6c">
         <name>CANFD0_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6d">
         <name>DAC0_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6e">
         <name>SPI0_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6f">
         <name>SPI1_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-70">
         <name>UART1_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-71">
         <name>UART2_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-72">
         <name>UART0_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-73">
         <name>TIMG0_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-74">
         <name>TIMG6_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-75">
         <name>TIMA0_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-76">
         <name>TIMA1_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>TIMG7_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>TIMG12_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>I2C0_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>I2C1_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>AES_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>RTC_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>DMA_IRQHandler</name>
         <value>0x1e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-7f">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-80">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-81">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-82">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-83">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-84">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-85">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-86">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-90">
         <name>DL_Common_delayCycles</name>
         <value>0x1d1</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-9b">
         <name>_c_int00_noinit_noargs</name>
         <value>0x17d</value>
         <object_component_ref idref="oc-4d"/>
      </symbol>
      <symbol id="sm-9c">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-a6">
         <name>_system_pre_init</name>
         <value>0x1df</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-b4">
         <name>abort</name>
         <value>0x1e3</value>
         <object_component_ref idref="oc-82"/>
      </symbol>
      <symbol id="sm-b5">
         <name>C$$EXIT</name>
         <value>0x1e2</value>
         <object_component_ref idref="oc-82"/>
      </symbol>
      <symbol id="sm-b8">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
