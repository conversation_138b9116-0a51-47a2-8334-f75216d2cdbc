******************************************************************************
            TI ARM Clang Linker PC v3.2.2                      
******************************************************************************
>> Linked Sat Jul 19 22:41:55 2025

OUTPUT FILE NAME:   <AD9959_ctrl.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000006e5


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000007d8  0001f828  R  X
  SRAM                  20200000   00008000  0000020d  00007df3  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000007d8   000007d8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000006f0   000006f0    r-x .text
  000007b0    000007b0    00000028   00000028    r-- .cinit
20200000    20200000    0000000d   00000000    rw-
  20200000    20200000    0000000d   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000006f0     
                  000000c0    0000012c     AD9959.o (.text.AD9959_WriteData)
                  000001ec    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000002d0    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000036a    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000036c    00000078     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000003e4    0000006c     AD9959.o (.text.AD9959_Init)
                  00000450    0000005c     empty.o (.text.main)
                  000004ac    00000050     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000004fc    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_COMP_0_init)
                  00000544    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00000586    00000002     AD9959.o (.text.delay1)
                  00000588    00000040     AD9959.o (.text.AD9959_Set_Fre)
                  000005c8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000608    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000644    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000067e    00000002     --HOLE-- [fill = 0]
                  00000680    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000006b4    0000002e     AD9959.o (.text.AD9959_Set_Amp)
                  000006e2    00000002     --HOLE-- [fill = 0]
                  000006e4    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0000070c    00000024     AD9959.o (.text.AD9959_Set_Phase)
                  00000730    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00000754    00000014     AD9959.o (.text.IO_Update)
                  00000768    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000077c    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  0000078e    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00000798    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000007a0    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000007a4    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000007a8    00000004            : exit.c.obj (.text:abort)
                  000007ac    00000004     --HOLE-- [fill = 0]

.cinit     0    000007b0    00000028     
                  000007b0    00000011     (.cinit..data.load) [load image, compression = lzss]
                  000007c1    00000003     --HOLE-- [fill = 0]
                  000007c4    00000008     (__TI_handler_table)
                  000007cc    00000008     (__TI_cinit_table)
                  000007d4    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    0000000d     UNINITIALIZED
                  20200000    00000008     AD9959.o (.data.ACC_FRE_FACTOR)
                  20200008    00000003     AD9959.o (.data.FR1_DATA)
                  2020000b    00000002     AD9959.o (.data.FR2_DATA)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       AD9959.o                       576    0         13     
       ti_msp_dl_config.o             288    0         0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       empty.o                        92     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         962    192       13     
                                                              
    D:/TISDK/mspm0_sdk_2_00_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         10     0         0      
                                                              
    D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     120    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         400    0         0      
                                                              
    D:\ticccs\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang/15.0.7/lib/armv6m-ti-none-eabi/libclang_rt.builtins.a
       muldf3.S.obj                   228    0         0      
       fixunsdfsi.S.obj               66     0         0      
       muldsi3.S.obj                  58     0         0      
       floatunsidf.S.obj              36     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         396    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      33        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   1768   225       525    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000007cc records: 1, size/record: 8, table size: 8
	.data: load addr=000007b0, load size=00000011 bytes, run addr=20200000, run size=0000000d bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000007c4 records: 2, size/record: 4, table size: 8
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
20200000  ACC_FRE_FACTOR                
000003e5  AD9959_Init                   
000006b5  AD9959_Set_Amp                
00000589  AD9959_Set_Fre                
0000070d  AD9959_Set_Phase              
000000c1  AD9959_WriteData              
0000036b  ADC0_IRQHandler               
0000036b  ADC1_IRQHandler               
0000036b  AES_IRQHandler                
000007a8  C$$EXIT                       
0000036b  CANFD0_IRQHandler             
0000036b  DAC0_IRQHandler               
0000078f  DL_Common_delayCycles         
0000036b  DMA_IRQHandler                
0000036b  Default_Handler               
20200008  FR1_DATA                      
2020000b  FR2_DATA                      
0000036b  GROUP0_IRQHandler             
0000036b  GROUP1_IRQHandler             
0000036b  HardFault_Handler             
0000036b  I2C0_IRQHandler               
0000036b  I2C1_IRQHandler               
00000755  IO_Update                     
0000036b  NMI_Handler                   
0000036b  PendSV_Handler                
0000036b  RTC_IRQHandler                
000007a1  Reset_Handler                 
0000036b  SPI0_IRQHandler               
0000036b  SPI1_IRQHandler               
0000036b  SVC_Handler                   
000004fd  SYSCFG_DL_COMP_0_init         
000004ad  SYSCFG_DL_GPIO_init           
000005c9  SYSCFG_DL_SYSCTL_init         
00000769  SYSCFG_DL_init                
00000681  SYSCFG_DL_initPower           
0000036b  SysTick_Handler               
0000036b  TIMA0_IRQHandler              
0000036b  TIMA1_IRQHandler              
0000036b  TIMG0_IRQHandler              
0000036b  TIMG12_IRQHandler             
0000036b  TIMG6_IRQHandler              
0000036b  TIMG7_IRQHandler              
0000036b  TIMG8_IRQHandler              
0000036b  UART0_IRQHandler              
0000036b  UART1_IRQHandler              
0000036b  UART2_IRQHandler              
0000036b  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
000007cc  __TI_CINIT_Base               
000007d4  __TI_CINIT_Limit              
000007d4  __TI_CINIT_Warm               
000007c4  __TI_Handler_Table_Base       
000007cc  __TI_Handler_Table_Limit      
00000609  __TI_auto_init_nobinit_nopinit
0000036d  __TI_decompress_lzss          
0000077d  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000545  __aeabi_d2uiz                 
000001ed  __aeabi_dmul                  
00000799  __aeabi_memcpy                
00000799  __aeabi_memcpy4               
00000799  __aeabi_memcpy8               
00000731  __aeabi_ui2d                  
ffffffff  __binit__                     
00000545  __fixunsdfsi                  
00000731  __floatunsidf                 
UNDEFED   __mpu_init                    
000001ed  __muldf3                      
00000645  __muldsi3                     
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
000006e5  _c_int00_noargs               
UNDEFED   _system_post_cinit            
000007a5  _system_pre_init              
000007a9  abort                         
ffffffff  binit                         
00000587  delay1                        
00000000  interruptVectors              
00000451  main                          
000002d1  memcpy                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  AD9959_WriteData              
000001ed  __aeabi_dmul                  
000001ed  __muldf3                      
00000200  __STACK_SIZE                  
000002d1  memcpy                        
0000036b  ADC0_IRQHandler               
0000036b  ADC1_IRQHandler               
0000036b  AES_IRQHandler                
0000036b  CANFD0_IRQHandler             
0000036b  DAC0_IRQHandler               
0000036b  DMA_IRQHandler                
0000036b  Default_Handler               
0000036b  GROUP0_IRQHandler             
0000036b  GROUP1_IRQHandler             
0000036b  HardFault_Handler             
0000036b  I2C0_IRQHandler               
0000036b  I2C1_IRQHandler               
0000036b  NMI_Handler                   
0000036b  PendSV_Handler                
0000036b  RTC_IRQHandler                
0000036b  SPI0_IRQHandler               
0000036b  SPI1_IRQHandler               
0000036b  SVC_Handler                   
0000036b  SysTick_Handler               
0000036b  TIMA0_IRQHandler              
0000036b  TIMA1_IRQHandler              
0000036b  TIMG0_IRQHandler              
0000036b  TIMG12_IRQHandler             
0000036b  TIMG6_IRQHandler              
0000036b  TIMG7_IRQHandler              
0000036b  TIMG8_IRQHandler              
0000036b  UART0_IRQHandler              
0000036b  UART1_IRQHandler              
0000036b  UART2_IRQHandler              
0000036b  UART3_IRQHandler              
0000036d  __TI_decompress_lzss          
000003e5  AD9959_Init                   
00000451  main                          
000004ad  SYSCFG_DL_GPIO_init           
000004fd  SYSCFG_DL_COMP_0_init         
00000545  __aeabi_d2uiz                 
00000545  __fixunsdfsi                  
00000587  delay1                        
00000589  AD9959_Set_Fre                
000005c9  SYSCFG_DL_SYSCTL_init         
00000609  __TI_auto_init_nobinit_nopinit
00000645  __muldsi3                     
00000681  SYSCFG_DL_initPower           
000006b5  AD9959_Set_Amp                
000006e5  _c_int00_noargs               
0000070d  AD9959_Set_Phase              
00000731  __aeabi_ui2d                  
00000731  __floatunsidf                 
00000755  IO_Update                     
00000769  SYSCFG_DL_init                
0000077d  __TI_decompress_none          
0000078f  DL_Common_delayCycles         
00000799  __aeabi_memcpy                
00000799  __aeabi_memcpy4               
00000799  __aeabi_memcpy8               
000007a1  Reset_Handler                 
000007a5  _system_pre_init              
000007a8  C$$EXIT                       
000007a9  abort                         
000007c4  __TI_Handler_Table_Base       
000007cc  __TI_CINIT_Base               
000007cc  __TI_Handler_Table_Limit      
000007d4  __TI_CINIT_Limit              
000007d4  __TI_CINIT_Warm               
20200000  ACC_FRE_FACTOR                
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200008  FR1_DATA                      
2020000b  FR2_DATA                      
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[96 symbols]
