/**********************************************************
                       康威电子
功能：stm32f103rct6控制AD9959模块输出点频正弦波，频率，幅度，相位独立可调
接口：控制引脚接口请参照AD9959.h
时间：
版本：2.6
作者：康威电子
其他：本程序只供学习使用，盗版必究。

更多电子需求，请到淘宝店，康威电子竭诚为您服务 ^_^
https://kvdz.taobao.com/ 
**********************************************************/

#include "stm32_config.h"
#include "stdio.h"
#include "AD9959.h"

int main(void)
{
	MY_NVIC_PriorityGroup_Config(NVIC_PriorityGroup_2);	//设置中断分组
	delay_init(72);	//初始化延时函数
	delay_ms(500);//延时一会儿，等待上电稳定,确保AD9959比控制板先上电。
	
	//代码移植建议
	//1.修改头文件AD9959.h中，自己控制板实际需要使用哪些控制引脚。如CS脚改成PA0控制，则定义"#define CS	PAout(0)" 
	//2.修改C文件AD9959.c中，AD9959_Init函数，所有用到管脚的GPIO输出功能初始化
	//3.完成
	
	AD9959_Init();								//初始化控制AD9959需要用到的IO口,及寄存器
	AD9959_Set_Fre(CH0, 100000);	//设置通道0频率100000Hz
	AD9959_Set_Fre(CH1, 100000);	//设置通道1频率100000Hz
	AD9959_Set_Fre(CH2, 100000);	//设置通道2频率100000Hz
	AD9959_Set_Fre(CH3, 100000);	//设置通道3频率100000Hz
		
	AD9959_Set_Amp(CH0, 1023); 		//设置通道0幅度控制值1023，范围0~1023
	AD9959_Set_Amp(CH1, 1023); 		//设置通道1幅度控制值1023，范围0~1023
	AD9959_Set_Amp(CH2, 1023); 		//设置通道2幅度控制值1023，范围0~1023
	AD9959_Set_Amp(CH3, 1023); 		//设置通道3幅度控制值1023，范围0~1023

	AD9959_Set_Phase(CH0, 0);			//设置通道0相位控制值0(0度)，范围0~16383
	AD9959_Set_Phase(CH1, 4096);	//设置通道1相位控制值4096(90度)，范围0~16383
	AD9959_Set_Phase(CH2, 8192);	//设置通道2相位控制值8192(180度)，范围0~16383
	AD9959_Set_Phase(CH3, 12288);	//设置通道3相位控制值12288(270度)，范围0~16383
	IO_Update();	//AD9959更新数据,调用此函数后，上述操作生效！！！！
	while(1);
}


